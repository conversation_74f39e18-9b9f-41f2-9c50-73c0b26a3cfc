<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Page Progress Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .page-progress {
            margin: 15px 0;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 8px;
            border: 1px solid #e9ecef;
        }
        .page-info {
            display: flex;
            flex-direction: column;
            gap: 10px;
        }
        .current-page {
            font-size: 16px;
            font-weight: 600;
            color: #495057;
            text-align: center;
        }
        .page-progress-bar {
            width: 100%;
            height: 12px;
            background: #e9ecef;
            border-radius: 6px;
            overflow: hidden;
        }
        .page-progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #007bff 0%, #0056b3 100%);
            border-radius: 6px;
            transition: width 0.5s ease;
            width: 0%;
        }
        button {
            padding: 10px 20px;
            margin: 5px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-weight: 600;
        }
        .btn-primary {
            background: #007bff;
            color: white;
        }
        .btn-secondary {
            background: #6c757d;
            color: white;
        }
        .status-log {
            height: 150px;
            overflow-y: auto;
            border: 1px solid #ddd;
            padding: 10px;
            background: #f8f9fa;
            font-family: monospace;
            font-size: 12px;
        }
        .status-display {
            padding: 10px;
            background: #e7f3ff;
            border-radius: 4px;
            margin: 10px 0;
            font-weight: 600;
            text-align: center;
        }
    </style>
</head>
<body>
    <h1>LinkedIn Page Progress Test</h1>
    
    <div class="test-section">
        <h3>Page Progress Display</h3>
        
        <div class="status-display" id="status-display">
            Ready to start page collection
        </div>
        
        <div class="page-progress">
            <div class="page-info">
                <span class="current-page">Page: <span id="current-page">1</span> / <span id="total-pages">4</span></span>
                <div class="page-progress-bar">
                    <div class="page-progress-fill" id="progress-fill"></div>
                </div>
            </div>
        </div>
        
        <button class="btn-primary" onclick="startPageTest()">Start Page Collection Test</button>
        <button class="btn-secondary" onclick="resetTest()">Reset</button>
    </div>

    <div class="test-section">
        <h3>Status Log</h3>
        <div class="status-log" id="status-log"></div>
    </div>

    <script>
        let testInterval;
        let currentPage = 1;
        let totalPages = 4;

        function updatePageProgress(page, status) {
            document.getElementById('current-page').textContent = page;
            document.getElementById('total-pages').textContent = totalPages;
            document.getElementById('status-display').textContent = status;
            
            const progressPercentage = (page / totalPages) * 100;
            document.getElementById('progress-fill').style.width = `${progressPercentage}%`;
        }

        function logStatus(message) {
            const log = document.getElementById('status-log');
            const timestamp = new Date().toLocaleTimeString();
            log.innerHTML += `[${timestamp}] ${message}\n`;
            log.scrollTop = log.scrollHeight;
        }

        function startPageTest() {
            if (testInterval) clearInterval(testInterval);
            
            currentPage = 1;
            
            logStatus('🚀 Starting collection from 4 pages...');
            updatePageProgress(1, 'Processing page 1');
            
            testInterval = setInterval(() => {
                if (currentPage <= totalPages) {
                    // Simulate page processing
                    logStatus(`Processing page ${currentPage}`);
                    updatePageProgress(currentPage, `Processing page ${currentPage}`);
                    
                    setTimeout(() => {
                        logStatus(`Completed page ${currentPage}`);
                        updatePageProgress(currentPage, `Completed page ${currentPage}`);
                        
                        currentPage++;
                        
                        if (currentPage <= totalPages) {
                            setTimeout(() => {
                                logStatus(`Navigating to page ${currentPage}`);
                                updatePageProgress(currentPage, `Navigating to page ${currentPage}`);
                            }, 500);
                        } else {
                            clearInterval(testInterval);
                            logStatus(`All pages completed (4/4)`);
                            updatePageProgress(4, 'All pages completed (4/4)');
                            document.getElementById('progress-fill').style.width = '100%';
                        }
                    }, 2000);
                }
            }, 4000);
        }

        function resetTest() {
            if (testInterval) clearInterval(testInterval);
            currentPage = 1;
            
            updatePageProgress(1, 'Ready to start page collection');
            document.getElementById('progress-fill').style.width = '0%';
            document.getElementById('status-log').innerHTML = '';
            
            logStatus('🔄 Test reset');
        }
    </script>
</body>
</html>
