<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Multi-Page Collection Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .progress-container {
            margin: 15px 0;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 8px;
            border: 1px solid #e9ecef;
        }
        .progress-stats {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
            font-weight: 600;
        }
        .progress-bar {
            width: 100%;
            height: 8px;
            background: #e9ecef;
            border-radius: 4px;
            overflow: hidden;
            margin-bottom: 5px;
        }
        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #28a745 0%, #20c997 100%);
            border-radius: 4px;
            transition: width 0.5s ease;
            width: 0%;
        }
        .progress-text {
            font-size: 12px;
            color: #6c757d;
            text-align: center;
            font-weight: 500;
        }
        button {
            padding: 10px 20px;
            margin: 5px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-weight: 600;
        }
        .btn-primary {
            background: #007bff;
            color: white;
        }
        .btn-secondary {
            background: #6c757d;
            color: white;
        }
        .status-log {
            height: 200px;
            overflow-y: auto;
            border: 1px solid #ddd;
            padding: 10px;
            background: #f8f9fa;
            font-family: monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <h1>LinkedIn Multi-Page Collection Test</h1>
    
    <div class="test-section">
        <h3>Progress Display Test</h3>
        <div class="progress-container">
            <div class="progress-stats">
                <span>Page: <span id="current-page">1</span>/<span id="total-pages">4</span></span>
                <span>Found: <span id="profiles-found">0</span> profiles</span>
            </div>
            <div class="progress-bar">
                <div class="progress-fill" id="progress-fill"></div>
            </div>
            <div class="progress-text" id="progress-text">Starting collection...</div>
        </div>
        
        <button class="btn-primary" onclick="startTest()">Start Multi-Page Test</button>
        <button class="btn-secondary" onclick="pauseTest()">Pause</button>
        <button class="btn-secondary" onclick="resetTest()">Reset</button>
    </div>

    <div class="test-section">
        <h3>Status Log</h3>
        <div class="status-log" id="status-log"></div>
    </div>

    <script>
        let testInterval;
        let currentPage = 1;
        let totalPages = 4;
        let profilesFound = 0;
        let isPaused = false;

        function updateProgress(page, total, profiles, message) {
            document.getElementById('current-page').textContent = page;
            document.getElementById('total-pages').textContent = total;
            document.getElementById('profiles-found').textContent = profiles;
            document.getElementById('progress-text').textContent = message;
            
            const progressPercentage = ((page - 1) / total) * 100;
            document.getElementById('progress-fill').style.width = `${progressPercentage}%`;
        }

        function logStatus(message) {
            const log = document.getElementById('status-log');
            const timestamp = new Date().toLocaleTimeString();
            log.innerHTML += `[${timestamp}] ${message}\n`;
            log.scrollTop = log.scrollHeight;
        }

        function startTest() {
            if (testInterval) clearInterval(testInterval);
            
            currentPage = 1;
            profilesFound = 0;
            isPaused = false;
            
            logStatus('🚀 Starting multi-page collection test...');
            updateProgress(1, 4, 0, 'Starting collection...');
            
            testInterval = setInterval(() => {
                if (isPaused) {
                    logStatus('⏸️ Collection paused');
                    return;
                }
                
                if (currentPage <= totalPages) {
                    // Simulate navigation
                    logStatus(`🔄 Navigating to page ${currentPage}...`);
                    updateProgress(currentPage, totalPages, profilesFound, `Navigating to page ${currentPage}...`);
                    
                    setTimeout(() => {
                        // Simulate profile collection
                        const newProfiles = Math.floor(Math.random() * 10) + 5; // 5-14 profiles
                        profilesFound += newProfiles;
                        
                        logStatus(`✅ Found ${newProfiles} profiles on page ${currentPage}`);
                        updateProgress(currentPage, totalPages, profilesFound, `Found ${newProfiles} profiles on page ${currentPage}`);
                        
                        currentPage++;
                        
                        if (currentPage > totalPages) {
                            clearInterval(testInterval);
                            logStatus(`🎉 Collection complete! Found ${profilesFound} profiles from ${totalPages} pages`);
                            updateProgress(totalPages, totalPages, profilesFound, `✅ Collection complete! Found ${profilesFound} profiles`);
                            document.getElementById('progress-fill').style.width = '100%';
                        }
                    }, 1000);
                }
            }, 3000);
        }

        function pauseTest() {
            isPaused = !isPaused;
            const button = event.target;
            
            if (isPaused) {
                button.textContent = 'Resume';
                logStatus('⏸️ Collection paused by user');
                updateProgress(currentPage, totalPages, profilesFound, '⏸️ Collection paused');
            } else {
                button.textContent = 'Pause';
                logStatus('▶️ Collection resumed');
                updateProgress(currentPage, totalPages, profilesFound, `Resuming collection on page ${currentPage}...`);
            }
        }

        function resetTest() {
            if (testInterval) clearInterval(testInterval);
            currentPage = 1;
            profilesFound = 0;
            isPaused = false;
            
            updateProgress(1, 4, 0, 'Ready to start...');
            document.getElementById('status-log').innerHTML = '';
            document.querySelector('button[onclick="pauseTest()"]').textContent = 'Pause';
            
            logStatus('🔄 Test reset');
        }
    </script>
</body>
</html>
